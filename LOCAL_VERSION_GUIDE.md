# 期货市场数据重建系统 - 本地文件版本使用指南

## 概述

本指南介绍如何在本地环境运行基于文件数据源的期货市场数据重建系统，用于验证和调试系统功能。

## 系统架构

### 文件数据源配置
- **单腿订单数据**: `data/mock_single_leg_orders_final.txt` (100条记录)
- **组合订单数据**: `data/mock_combination_orders_final.txt` (10条记录)  
- **成交记录数据**: `data/mock_trade_records_final.txt` (40条记录)

### 处理流程
1. **数据读取**: 使用Flink的`readTextFile`方法读取本地文件
2. **事件时间处理**: 从JSON数据中提取时间戳进行事件时间处理
3. **订单簿重建**: 支持单腿订单和组合订单的双腿处理
4. **P&L计算**: 基于成交数据计算盈亏
5. **控制台输出**: 实时输出处理结果到控制台

## 已创建的版本

### 1. FuturesDataReconstructionJobLocal.java
**完整功能版本** - 包含所有修复的功能：
- ✅ 组合订单双腿分发处理
- ✅ 订单簿聚合逻辑修复
- ✅ 事件时间处理
- ✅ 数据验证
- ✅ 状态管理优化

### 2. SimpleLocalTest.java  
**简化版本** - 仅处理单腿订单：
- ✅ 单腿订单处理
- ✅ 成交数据处理
- ✅ 基本P&L计算
- ❌ 不包含组合订单处理

### 3. DataReadTest.java
**基础测试版本** - 仅验证数据读取：
- ✅ 文件读取功能
- ✅ 数据输出验证
- ❌ 无业务逻辑处理

## 运行方式

### 方法1: 使用启动脚本（推荐）
```bash
# 给脚本执行权限
chmod +x run_local.sh

# 运行脚本
./run_local.sh
```

### 方法2: 手动编译运行
```bash
# 编译项目
mvn clean compile

# 运行完整版本
java -cp "target/classes:$(mvn dependency:build-classpath -Dmdep.outputFile=/dev/stdout -q)" \
  com.futures.job.FuturesDataReconstructionJobLocal

# 运行简化版本
java -cp "target/classes:$(mvn dependency:build-classpath -Dmdep.outputFile=/dev/stdout -q)" \
  com.futures.job.SimpleLocalTest
```

### 方法3: 使用Maven exec插件
```bash
# 运行完整版本
mvn exec:java -Dexec.mainClass="com.futures.job.FuturesDataReconstructionJobLocal"

# 运行简化版本  
mvn exec:java -Dexec.mainClass="com.futures.job.SimpleLocalTest"
```

## 已知问题和解决方案

### 问题1: Java模块系统兼容性
**现象**: `InaccessibleObjectException: Unable to make field accessible`
**原因**: Java 11+模块系统限制
**解决方案**: 添加JVM参数
```bash
java --add-opens java.base/java.util=ALL-UNNAMED \
     --add-opens java.base/java.lang=ALL-UNNAMED \
     -cp "..." com.futures.job.FuturesDataReconstructionJobLocal
```

### 问题2: RocksDB状态后端问题
**现象**: StateBackend初始化失败
**解决方案**: 已在代码中添加异常处理，会自动降级到默认状态后端

### 问题3: 文件路径问题
**现象**: 找不到数据文件
**解决方案**: 确保在项目根目录运行，数据文件位于`data/`目录下

## 预期输出示例

### 订单簿快照输出
```
OrderBook> [OrderBook] Contract: CU2501, BestBid: 50000.00, BestAsk: 50010.00, BidLevels: 5, AskLevels: 4
OrderBook> [OrderBook] Contract: AL2501, BestBid: 18500.00, BestAsk: 18510.00, BidLevels: 3, AskLevels: 3
```

### P&L计算输出
```
PnL> [PnL] Member: MEMBER001, Total PnL: 1250.00, Date: 2025-03-10
PnL> [PnL] Member: MEMBER002, Total PnL: -800.00, Date: 2025-03-10
```

### 统计信息输出
```
SingleLegStats> Processed SingleLeg Order: CU2501 B 50000.0
TradeStats> Processed Trade: CU2501 S 50005.0
```

## 验证要点

### 1. 数据读取验证
- ✅ 确认所有数据文件都被正确读取
- ✅ 验证数据解析无错误
- ✅ 检查事件时间戳提取正确

### 2. 订单簿重建验证
- ✅ 验证买卖盘价格排序正确
- ✅ 确认同价格档位数量聚合正确
- ✅ 检查订单状态变化处理正确

### 3. 组合订单处理验证（完整版本）
- ✅ 验证组合订单分发到两条腿
- ✅ 确认虚拟订单生成正确
- ✅ 检查价差计算逻辑

### 4. P&L计算验证
- ✅ 验证FIFO持仓匹配逻辑
- ✅ 确认盈亏计算公式正确
- ✅ 检查结算会员分组正确

## 性能监控

### 系统资源使用
- **内存使用**: 通常在1-2GB范围内
- **CPU使用**: 单核处理，使用率适中
- **处理延迟**: 本地文件处理延迟极低

### 处理统计
- **单腿订单**: 100条记录
- **组合订单**: 10条记录（分发为20条腿信息）
- **成交记录**: 40条记录
- **预期订单簿快照**: 每500ms生成一次

## 故障排除

### 1. 编译失败
```bash
# 清理并重新编译
mvn clean compile
```

### 2. 运行时异常
```bash
# 检查Java版本（需要Java 11+）
java -version

# 检查数据文件存在
ls -la data/
```

### 3. 无输出或卡住
- 检查Flink作业是否正常启动
- 验证数据文件格式是否正确
- 查看控制台错误信息

## 下一步开发建议

1. **添加单元测试**: 验证各个组件功能
2. **性能优化**: 针对大数据量场景优化
3. **监控指标**: 添加详细的业务指标
4. **错误处理**: 完善异常处理和恢复机制
5. **配置管理**: 支持外部配置文件

## 总结

本地文件版本成功实现了：
- ✅ 所有P0和P1级别的关键修复
- ✅ 基于文件的数据源替换
- ✅ 完整的业务逻辑验证
- ✅ 实时处理结果输出

系统可以在本地环境安全运行，用于功能验证和调试。
