// ============ 4. 配置文件和部署脚本 ============

/**
* Maven依赖配置 (pom.xml)
  */
  /*
  <dependencies>
  <dependency>
  <groupId>org.apache.flink</groupId>
  <artifactId>flink-streaming-java_2.12</artifactId>
  <version>1.17.0</version>
  </dependency>
  <dependency>
  <groupId>org.apache.flink</groupId>
  <artifactId>flink-connector-kafka_2.12</artifactId>
  <version>1.17.0</version>
  </dependency>
  <dependency>
  <groupId>org.apache.flink</groupId>
  <artifactId>flink-json</artifactId>
  <version>1.17.0</version>
  </dependency>
  <dependency>
  <groupId>org.apache.flink</groupId>
  <artifactId>flink-statebackend-rocksdb_2.12</artifactId>
  <version>1.17.0</version>
  </dependency>
  <dependency>
  <groupId>com.fasterxml.jackson.core</groupId>
  <artifactId>jackson-databind</artifactId>
  <version>2.14.2</version>
  </dependency>
  </dependencies>
  */

/**
* Flink作业配置建议
  */
  /*
# 状态后端配置
state.backend: rocksdb
state.checkpoints.dir: hdfs://namenode:9000/flink/checkpoints
state.backend.rocksdb.ttl.compaction.filter.enabled: true

# 检查点配置
execution.checkpointing.interval: 5s
execution.checkpointing.mode: EXACTLY_ONCE
execution.checkpointing.timeout: 10min
execution.checkpointing.max-concurrent-checkpoints: 1

# 资源配置
taskmanager.memory.process.size: 4g
taskmanager.memory.flink.size: 3g
taskmanager.numberOfTaskSlots: 4

# 网络配置
taskmanager.network.memory.fraction: 0.15
taskmanager.network.memory.min: 256mb
taskmanager.network.memory.max: 1gb
*/

/**
* 启动脚本示例
  */
  /*
  #!/bin/bash
# start_futures_job.sh

FLINK_HOME=/opt/flink
JAR_PATH=/path/to/futures-data-reconstruction-1.0.jar
MAIN_CLASS=com.example.FuturesDataReconstructionJob

$FLINK_HOME/bin/flink run \
--class $MAIN_CLASS \
--parallelism 4 \
--jobmanager yarn-cluster \
--yarncontainer 2 \
--yarnjobManagerMemory 2048 \
--yarntaskManagerMemory 4096 \
$JAR_PATH
*/