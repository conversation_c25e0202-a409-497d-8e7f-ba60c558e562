#!/bin/bash

# 期货市场数据重建系统 - 本地文件版本启动脚本

echo "=========================================="
echo "期货市场数据重建系统 - 本地文件版本"
echo "=========================================="

# 检查Java环境
if ! command -v java &> /dev/null; then
    echo "错误: 未找到Java环境，请确保已安装Java 11或更高版本"
    exit 1
fi

# 检查Maven环境
if ! command -v mvn &> /dev/null; then
    echo "错误: 未找到Maven环境，请确保已安装Maven"
    exit 1
fi

# 检查数据文件
echo "检查数据文件..."
if [ ! -f "data/mock_single_leg_orders_final.txt" ]; then
    echo "错误: 未找到单腿订单数据文件 data/mock_single_leg_orders_final.txt"
    exit 1
fi

if [ ! -f "data/mock_combination_orders_final.txt" ]; then
    echo "错误: 未找到组合订单数据文件 data/mock_combination_orders_final.txt"
    exit 1
fi

if [ ! -f "data/mock_trade_records_final.txt" ]; then
    echo "错误: 未找到成交记录数据文件 data/mock_trade_records_final.txt"
    exit 1
fi

echo "✅ 所有数据文件检查通过"

# 统计数据文件行数
single_leg_count=$(wc -l < data/mock_single_leg_orders_final.txt)
combination_count=$(wc -l < data/mock_combination_orders_final.txt)
trade_count=$(wc -l < data/mock_trade_records_final.txt)

echo "数据文件统计:"
echo "  - 单腿订单: $single_leg_count 条"
echo "  - 组合订单: $combination_count 条"
echo "  - 成交记录: $trade_count 条"

# 创建临时目录
echo "创建临时目录..."
mkdir -p /tmp/flink-checkpoints-local
mkdir -p logs

# 编译项目
echo "编译项目..."
mvn clean compile -q
if [ $? -ne 0 ]; then
    echo "错误: 项目编译失败"
    exit 1
fi

echo "✅ 项目编译成功"

# 设置JVM参数
export JAVA_OPTS="-Xmx2g -Xms1g -XX:+UseG1GC"

# 运行本地版本
echo "启动期货市场数据重建系统..."
echo "注意: 系统将处理本地文件数据并输出到控制台"
echo "按 Ctrl+C 停止程序"
echo "=========================================="

# 使用Maven exec插件运行
mvn exec:java \
    -Dexec.mainClass="com.futures.job.FuturesDataReconstructionJobLocal" \
    -Dexec.args="" \
    -Dexec.cleanupDaemonThreads=false \
    -q

echo "程序已退出"
