package com.futures.datagen.model;

// 盈亏结果
public class PnLResult {
    private String settle_memb_unfy_cde;
    private double total_pnl;
    private String date;
    private long timestamp;

    public PnLResult(String settle_memb_unfy_cde, double total_pnl, String date) {
        this.settle_memb_unfy_cde = settle_memb_unfy_cde;
        this.total_pnl = total_pnl;
        this.date = date;
        this.timestamp = System.currentTimeMillis();
    }

    // getter/setter方法
    public String getSettle_memb_unfy_cde() { return settle_memb_unfy_cde; }
    public double getTotal_pnl() { return total_pnl; }
    public String getDate() { return date; }
    public long getTimestamp() { return timestamp; }
}
