package com.futures.datagen.model;

// 单腿委托事件
public class SingleLegOrderEvent {
    private String ord_nbr;
    private String contract_cde;
    private String b_s_tag;
    private double ord_prc;
    private long ord_vol;
    private long rmn_vol;  // 剩余数量
    private String ord_sts;  // 订单状态
    private long event_timestamp;

    public SingleLegOrderEvent() {}

    // getter/setter方法
    public String getOrd_nbr() { return ord_nbr; }
    public void setOrd_nbr(String ord_nbr) { this.ord_nbr = ord_nbr; }

    public String getContract_cde() { return contract_cde; }
    public void setContract_cde(String contract_cde) { this.contract_cde = contract_cde; }

    public String getB_s_tag() { return b_s_tag; }
    public void setB_s_tag(String b_s_tag) { this.b_s_tag = b_s_tag; }

    public double getOrd_prc() { return ord_prc; }
    public void setOrd_prc(double ord_prc) { this.ord_prc = ord_prc; }

    public long getOrd_vol() { return ord_vol; }
    public void setOrd_vol(long ord_vol) { this.ord_vol = ord_vol; }

    public long getRmn_vol() { return rmn_vol; }
    public void setRmn_vol(long rmn_vol) { this.rmn_vol = rmn_vol; }

    public String getOrd_sts() { return ord_sts; }
    public void setOrd_sts(String ord_sts) { this.ord_sts = ord_sts; }

    public long getEventTimestamp() { return event_timestamp; }
    public void setEventTimestamp(long timestamp) { this.event_timestamp = timestamp; }
}