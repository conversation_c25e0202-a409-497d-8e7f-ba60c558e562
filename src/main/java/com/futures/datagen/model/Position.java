package com.futures.datagen.model;

// 持仓信息
public class Position {
    private String contract_cde;
    private String b_s_tag;
    private double open_price;
    private long volume;
    private long timestamp;

    public Position(String contract_cde, String b_s_tag, double open_price, long volume) {
        this.contract_cde = contract_cde;
        this.b_s_tag = b_s_tag;
        this.open_price = open_price;
        this.volume = volume;
        this.timestamp = System.currentTimeMillis();
    }

    // getter/setter方法
    public String getContract_cde() { return contract_cde; }
    public String getB_s_tag() { return b_s_tag; }
    public double getOpen_price() { return open_price; }
    public long getVolume() { return volume; }
    public void setVolume(long volume) { this.volume = volume; }
    public long getTimestamp() { return timestamp; }
}
