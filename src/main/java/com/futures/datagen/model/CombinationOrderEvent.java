package com.futures.datagen.model;

import java.io.Serializable;

// 组合委托事件
public class CombinationOrderEvent implements Serializable {
    private static final long serialVersionUID = 1L;
    private String ord_nbr;
    private String leg_1_contract_cde;
    private String leg_2_contract_cde;
    private String b_s_tag;
    private double ord_prc;  // 价差
    private long ord_vol;
    private long rmn_vol;
    private String ord_sts;
    private long event_timestamp;

    public CombinationOrderEvent() {}

    // getter/setter方法
    public String getOrd_nbr() { return ord_nbr; }
    public void setOrd_nbr(String ord_nbr) { this.ord_nbr = ord_nbr; }

    public String getLeg_1_contract_cde() { return leg_1_contract_cde; }
    public void setLeg_1_contract_cde(String leg_1_contract_cde) { this.leg_1_contract_cde = leg_1_contract_cde; }

    public String getLeg_2_contract_cde() { return leg_2_contract_cde; }
    public void setLeg_2_contract_cde(String leg_2_contract_cde) { this.leg_2_contract_cde = leg_2_contract_cde; }

    public String getB_s_tag() { return b_s_tag; }
    public void setB_s_tag(String b_s_tag) { this.b_s_tag = b_s_tag; }

    public double getOrd_prc() { return ord_prc; }
    public void setOrd_prc(double ord_prc) { this.ord_prc = ord_prc; }

    public long getOrd_vol() { return ord_vol; }
    public void setOrd_vol(long ord_vol) { this.ord_vol = ord_vol; }

    public long getRmn_vol() { return rmn_vol; }
    public void setRmn_vol(long rmn_vol) { this.rmn_vol = rmn_vol; }

    public String getOrd_sts() { return ord_sts; }
    public void setOrd_sts(String ord_sts) { this.ord_sts = ord_sts; }

    public long getEventTimestamp() { return event_timestamp; }
    public void setEventTimestamp(long timestamp) { this.event_timestamp = timestamp; }
}

