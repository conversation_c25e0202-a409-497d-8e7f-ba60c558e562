package com.futures.job;

import com.futures.datagen.model.*;
import com.futures.function.OrderBookReconstructionFunction;
import com.futures.function.PnLCalculationFunction;
import com.futures.validation.OrderValidator;
import org.apache.flink.api.common.eventtime.WatermarkStrategy;
import org.apache.flink.streaming.api.datastream.DataStream;
import org.apache.flink.streaming.api.environment.StreamExecutionEnvironment;
import org.apache.flink.api.common.functions.MapFunction;
import org.apache.flink.shaded.jackson2.com.fasterxml.jackson.databind.ObjectMapper;

import java.time.Duration;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;

/**
 * 简化的本地测试版本 - 用于验证基本功能
 */
public class SimpleLocalTest {

    private static final ObjectMapper objectMapper = new ObjectMapper();

    public static void main(String[] args) throws Exception {
        System.out.println("========================================");
        System.out.println("期货市场数据重建系统 - 简化本地测试版本");
        System.out.println("========================================");

        // 创建Flink执行环境
        StreamExecutionEnvironment env = StreamExecutionEnvironment.getExecutionEnvironment();
        env.setParallelism(1); // 使用单并行度简化调试

        // 配置检查点
        env.enableCheckpointing(30000); // 30秒检查点间隔

        System.out.println("正在读取数据文件...");

        // ============ 文件数据源配置 ============

        // 单腿委托数据源
        DataStream<String> singleLegStringStream = env
                .readTextFile("data/mock_single_leg_orders_final.txt")
                .assignTimestampsAndWatermarks(
                        WatermarkStrategy.<String>forBoundedOutOfOrderness(Duration.ofSeconds(1))
                                .withTimestampAssigner((element, recordTimestamp) -> {
                                    try {
                                        Map<String, Object> jsonMap = objectMapper.readValue(element, Map.class);
                                        String ordDt = getString(jsonMap, "ord_dt");
                                        String ordTm = getString(jsonMap, "ord_tm");
                                        return parseTimestamp(ordDt, ordTm);
                                    } catch (Exception e) {
                                        return System.currentTimeMillis();
                                    }
                                })
                );

        // 成交数据源
        DataStream<String> tradeStringStream = env
                .readTextFile("data/mock_trade_records_final.txt")
                .assignTimestampsAndWatermarks(
                        WatermarkStrategy.<String>forBoundedOutOfOrderness(Duration.ofSeconds(1))
                                .withTimestampAssigner((element, recordTimestamp) -> {
                                    try {
                                        Map<String, Object> jsonMap = objectMapper.readValue(element, Map.class);
                                        String trdDt = getString(jsonMap, "trd_dt");
                                        String trdTm = getString(jsonMap, "trd_tm");
                                        return parseTimestamp(trdDt, trdTm);
                                    } catch (Exception e) {
                                        return System.currentTimeMillis();
                                    }
                                })
                );

        System.out.println("正在解析数据...");

        // ============ 数据解析 ============

        // 解析单腿委托
        DataStream<SingleLegOrderEvent> singleLegStream = singleLegStringStream
                .map(new MapFunction<String, SingleLegOrderEvent>() {
                    @Override
                    public SingleLegOrderEvent map(String jsonStr) throws Exception {
                        return parseSingleLegOrder(jsonStr);
                    }
                }).name("Parse Single Leg Orders");

        // 解析成交数据
        DataStream<TradeEvent> tradeStream = tradeStringStream
                .map(new MapFunction<String, TradeEvent>() {
                    @Override
                    public TradeEvent map(String jsonStr) throws Exception {
                        return parseTradeEvent(jsonStr);
                    }
                }).name("Parse Trade Events");

        System.out.println("正在构建处理流...");

        // ============ 简化的订单簿重建流处理 ============

        // 只处理单腿订单的订单簿重建
        DataStream<OrderBookSnapshot> orderBookStream = singleLegStream
                .map(x -> (Object) x)
                .keyBy(order -> ((SingleLegOrderEvent) order).getContract_cde())
                .process(new OrderBookReconstructionFunction())
                .name("Order Book Reconstruction");

        // ============ 盈亏计算流处理 ============

        // 按结算会员分区并计算盈亏
        DataStream<PnLResult> pnlStream = tradeStream
                .keyBy(TradeEvent::getSettle_memb_unfy_cde)
                .process(new PnLCalculationFunction())
                .name("PnL Calculation");

        // ============ 输出配置 ============

        // 输出订单簿快照到控制台
        orderBookStream
                .map(snapshot -> {
                    return String.format("[OrderBook] Contract: %s, BestBid: %.2f, BestAsk: %.2f, BidLevels: %d, AskLevels: %d",
                            snapshot.getContract_cde(),
                            snapshot.getBestBid() != null ? snapshot.getBestBid() : 0.0,
                            snapshot.getBestAsk() != null ? snapshot.getBestAsk() : 0.0,
                            snapshot.getBids().size(),
                            snapshot.getAsks().size());
                })
                .print("OrderBook");

        // 输出盈亏结果到控制台
        pnlStream
                .map(pnl -> {
                    return String.format("[PnL] Member: %s, Total PnL: %.2f, Date: %s",
                            pnl.getSettle_memb_unfy_cde(),
                            pnl.getTotal_pnl(),
                            pnl.getDate());
                })
                .print("PnL");

        // 输出统计信息
        singleLegStream
                .map(order -> "Processed SingleLeg Order: " + order.getContract_cde() + " " + order.getB_s_tag() + " " + order.getOrd_prc())
                .print("SingleLegStats");

        tradeStream
                .map(trade -> "Processed Trade: " + trade.getContract_cde() + " " + trade.getB_s_tag() + " " + trade.getTrd_prc())
                .print("TradeStats");

        System.out.println("启动Flink作业...");

        // 启动作业
        env.execute("Simple Futures Data Reconstruction Test");
    }

    // ============ 辅助方法 ============

    public static SingleLegOrderEvent parseSingleLegOrder(String jsonStr) throws Exception {
        Map<String, Object> jsonMap = objectMapper.readValue(jsonStr, Map.class);

        SingleLegOrderEvent event = new SingleLegOrderEvent();
        event.setOrd_nbr(getString(jsonMap, "ord_nbr"));
        event.setContract_cde(getString(jsonMap, "contract_cde"));
        event.setB_s_tag(getString(jsonMap, "b_s_tag"));
        event.setOrd_prc(getDouble(jsonMap, "ord_prc"));
        event.setOrd_vol(getLong(jsonMap, "ord_vol"));
        event.setRmn_vol(getLong(jsonMap, "rmn_vol"));
        event.setOrd_sts(getString(jsonMap, "ord_sts"));

        // 解析时间戳
        String ordDt = getString(jsonMap, "ord_dt");
        String ordTm = getString(jsonMap, "ord_tm");
        event.setEventTimestamp(parseTimestamp(ordDt, ordTm));

        return event;
    }

    public static TradeEvent parseTradeEvent(String jsonStr) throws Exception {
        Map<String, Object> jsonMap = objectMapper.readValue(jsonStr, Map.class);

        TradeEvent event = new TradeEvent();
        event.setTrd_dt(getString(jsonMap, "trd_dt"));
        event.setOrd_nbr(getString(jsonMap, "ord_nbr"));
        event.setContract_cde(getString(jsonMap, "contract_cde"));
        event.setB_s_tag(getString(jsonMap, "b_s_tag"));
        event.setTrd_prc(getDouble(jsonMap, "trd_prc"));
        event.setTrd_vol(getLong(jsonMap, "trd_vol"));
        event.setTrd_type(getString(jsonMap, "trd_type"));
        event.setSettle_memb_unfy_cde(getString(jsonMap, "settle_memb_unfy_cde"));
        event.setOcpos_type(getString(jsonMap, "ocpos_type"));

        // 解析时间戳
        String trdDt = getString(jsonMap, "trd_dt");
        String trdTm = getString(jsonMap, "trd_tm");
        event.setEventTimestamp(parseTimestamp(trdDt, trdTm));

        return event;
    }

    private static String getString(Map<String, Object> map, String key) {
        Object value = map.get(key);
        return value != null ? value.toString() : "";
    }

    private static double getDouble(Map<String, Object> map, String key) {
        Object value = map.get(key);
        if (value == null) return 0.0;
        if (value instanceof Number) {
            return ((Number) value).doubleValue();
        }
        try {
            return Double.parseDouble(value.toString());
        } catch (NumberFormatException e) {
            return 0.0;
        }
    }

    private static long getLong(Map<String, Object> map, String key) {
        Object value = map.get(key);
        if (value == null) return 0L;
        if (value instanceof Number) {
            return ((Number) value).longValue();
        }
        try {
            return Long.parseLong(value.toString());
        } catch (NumberFormatException e) {
            return 0L;
        }
    }

    private static long parseTimestamp(String date, String time) {
        try {
            if (date == null || time == null || date.isEmpty() || time.isEmpty()) {
                return System.currentTimeMillis();
            }
            
            String dateTimeStr = date + " " + time;
            DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
            LocalDateTime dateTime = LocalDateTime.parse(dateTimeStr, formatter);
            
            return dateTime.atZone(java.time.ZoneId.systemDefault()).toInstant().toEpochMilli();
        } catch (Exception e) {
            return System.currentTimeMillis();
        }
    }
}
