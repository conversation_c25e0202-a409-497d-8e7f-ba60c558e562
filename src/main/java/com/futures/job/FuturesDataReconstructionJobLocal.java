package com.futures.job;

// ============ 本地文件版本的期货数据重建作业 ============

import com.futures.datagen.model.*;
import com.futures.function.OrderBookReconstructionFunction;
import com.futures.function.PnLCalculationFunction;
import com.futures.function.CombinationOrderSplitter;
import com.futures.validation.OrderValidator;
import org.apache.flink.api.common.eventtime.WatermarkStrategy;
import org.apache.flink.streaming.api.datastream.DataStream;
import org.apache.flink.streaming.api.datastream.SingleOutputStreamOperator;
import org.apache.flink.streaming.api.environment.StreamExecutionEnvironment;
import org.apache.flink.api.common.functions.MapFunction;
import org.apache.flink.shaded.jackson2.com.fasterxml.jackson.databind.ObjectMapper;
import org.apache.flink.contrib.streaming.state.EmbeddedRocksDBStateBackend;
import org.apache.flink.streaming.api.CheckpointingMode;

import java.time.Duration;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;

/**
 * 基于本地文件的期货数据重建作业
 * 用于本地环境验证和调试
 */
public class FuturesDataReconstructionJobLocal {

    private static final ObjectMapper objectMapper = new ObjectMapper();

    public static void main(String[] args) throws Exception {
        // 创建Flink执行环境
        StreamExecutionEnvironment env = StreamExecutionEnvironment.getExecutionEnvironment();
        env.setParallelism(2); // 本地环境使用较小的并行度

        // 配置RocksDB状态后端以防止内存泄漏
        try {
            env.setStateBackend(new EmbeddedRocksDBStateBackend(true));
        } catch (Exception e) {
            System.out.println("Warning: Failed to set RocksDB state backend, using default: " + e.getMessage());
        }

        // 配置检查点
        env.enableCheckpointing(10000); // 本地环境使用较长的检查点间隔
        env.getCheckpointConfig().setCheckpointingMode(CheckpointingMode.EXACTLY_ONCE);
        env.getCheckpointConfig().setMinPauseBetweenCheckpoints(1000);
        env.getCheckpointConfig().setCheckpointTimeout(60000);
        env.getCheckpointConfig().setMaxConcurrentCheckpoints(1);

        // ============ 文件数据源配置 ============

        // 单腿委托数据源 - 使用readTextFile方法
        DataStream<String> singleLegStringStream = env
                .readTextFile("data/mock_single_leg_orders_final.txt")
                .assignTimestampsAndWatermarks(
                        WatermarkStrategy.<String>forBoundedOutOfOrderness(Duration.ofSeconds(3))
                                .withTimestampAssigner((element, recordTimestamp) -> {
                                    try {
                                        // 从JSON中提取事件时间戳
                                        Map<String, Object> jsonMap = objectMapper.readValue(element, Map.class);
                                        String ordDt = getString(jsonMap, "ord_dt");
                                        String ordTm = getString(jsonMap, "ord_tm");
                                        return parseTimestamp(ordDt, ordTm);
                                    } catch (Exception e) {
                                        // 如果解析失败，使用当前时间
                                        return System.currentTimeMillis();
                                    }
                                })
                ).name("Single Leg Orders File Source");

        // 组合委托数据源
        DataStream<String> combStringStream = env
                .readTextFile("data/mock_combination_orders_final.txt")
                .assignTimestampsAndWatermarks(
                        WatermarkStrategy.<String>forBoundedOutOfOrderness(Duration.ofSeconds(3))
                                .withTimestampAssigner((element, recordTimestamp) -> {
                                    try {
                                        // 从JSON中提取事件时间戳
                                        Map<String, Object> jsonMap = objectMapper.readValue(element, Map.class);
                                        String ordDt = getString(jsonMap, "ord_dt");
                                        String ordTm = getString(jsonMap, "ord_tm");
                                        return parseTimestamp(ordDt, ordTm);
                                    } catch (Exception e) {
                                        // 如果解析失败，使用当前时间
                                        return System.currentTimeMillis();
                                    }
                                })
                ).name("Combination Orders File Source");

        // 成交数据源
        DataStream<String> tradeStringStream = env
                .readTextFile("data/mock_trade_records_final.txt")
                .assignTimestampsAndWatermarks(
                        WatermarkStrategy.<String>forBoundedOutOfOrderness(Duration.ofSeconds(3))
                                .withTimestampAssigner((element, recordTimestamp) -> {
                                    try {
                                        // 从JSON中提取事件时间戳
                                        Map<String, Object> jsonMap = objectMapper.readValue(element, Map.class);
                                        String trdDt = getString(jsonMap, "trd_dt");
                                        String trdTm = getString(jsonMap, "trd_tm");
                                        return parseTimestamp(trdDt, trdTm);
                                    } catch (Exception e) {
                                        // 如果解析失败，使用当前时间
                                        return System.currentTimeMillis();
                                    }
                                })
                ).name("Trades File Source");

        // ============ 数据解析 ============

        // 解析单腿委托
        DataStream<SingleLegOrderEvent> singleLegStream = singleLegStringStream
                .map(new MapFunction<String, SingleLegOrderEvent>() {
                    @Override
                    public SingleLegOrderEvent map(String jsonStr) throws Exception {
                        return parseSingleLegOrder(jsonStr);
                    }
                }).name("Parse Single Leg Orders");

        // 解析组合委托
        DataStream<CombinationOrderEvent> combStream = combStringStream
                .map(new MapFunction<String, CombinationOrderEvent>() {
                    @Override
                    public CombinationOrderEvent map(String jsonStr) throws Exception {
                        return parseCombinationOrder(jsonStr);
                    }
                }).name("Parse Combination Orders");

        // 解析成交数据
        DataStream<TradeEvent> tradeStream = tradeStringStream
                .map(new MapFunction<String, TradeEvent>() {
                    @Override
                    public TradeEvent map(String jsonStr) throws Exception {
                        return parseTradeEvent(jsonStr);
                    }
                }).name("Parse Trade Events");

        // ============ 订单簿重建流处理 ============

        // 处理组合订单分发
        SingleOutputStreamOperator<Object> combOrderSplitterStream = combStream
                .process(new CombinationOrderSplitter())
                .name("Split Combination Orders");

        // 获取分发后的两条腿数据流
        DataStream<CombinationOrderSplitter.CombinationOrderLegInfo> leg1Stream = combOrderSplitterStream
                .getSideOutput(CombinationOrderSplitter.LEG1_OUTPUT_TAG);
        DataStream<CombinationOrderSplitter.CombinationOrderLegInfo> leg2Stream = combOrderSplitterStream
                .getSideOutput(CombinationOrderSplitter.LEG2_OUTPUT_TAG);

        // 为第一条腿构建订单簿
        DataStream<OrderBookSnapshot> leg1OrderBookStream = singleLegStream
                .map(x -> (Object) x)
                .union(leg1Stream.map(x -> (Object) x))
                .keyBy(order -> {
                    if (order instanceof SingleLegOrderEvent) {
                        return ((SingleLegOrderEvent) order).getContract_cde();
                    } else if (order instanceof CombinationOrderSplitter.CombinationOrderLegInfo) {
                        return ((CombinationOrderSplitter.CombinationOrderLegInfo) order).getCurrentLegContract();
                    } else {
                        return "unknown";
                    }
                })
                .process(new OrderBookReconstructionFunction())
                .name("Leg1 Order Book Reconstruction");

        // 为第二条腿构建订单簿
        DataStream<OrderBookSnapshot> leg2OrderBookStream = singleLegStream
                .map(x -> (Object) x)
                .union(leg2Stream.map(x -> (Object) x))
                .keyBy(order -> {
                    if (order instanceof SingleLegOrderEvent) {
                        return ((SingleLegOrderEvent) order).getContract_cde();
                    } else if (order instanceof CombinationOrderSplitter.CombinationOrderLegInfo) {
                        return ((CombinationOrderSplitter.CombinationOrderLegInfo) order).getCurrentLegContract();
                    } else {
                        return "unknown";
                    }
                })
                .process(new OrderBookReconstructionFunction())
                .name("Leg2 Order Book Reconstruction");

        // 合并两条腿的订单簿快照
        DataStream<OrderBookSnapshot> orderBookStream = leg1OrderBookStream.union(leg2OrderBookStream);

        // ============ 盈亏计算流处理 ============

        // 按结算会员分区并计算盈亏
        DataStream<PnLResult> pnlStream = tradeStream
                .keyBy(TradeEvent::getSettle_memb_unfy_cde)
                .process(new PnLCalculationFunction())
                .name("PnL Calculation");

        // ============ 本地输出配置 ============

        // 输出订单簿快照到控制台（限制输出数量以避免刷屏）
        orderBookStream
                .map(snapshot -> {
                    return String.format("[OrderBook] Contract: %s, BestBid: %.2f, BestAsk: %.2f, BidDepth: %d, AskDepth: %d, Timestamp: %s",
                            snapshot.getContract_cde(),
                            snapshot.getBestBid() != null ? snapshot.getBestBid() : 0.0,
                            snapshot.getBestAsk() != null ? snapshot.getBestAsk() : 0.0,
                            snapshot.getBids().size(),
                            snapshot.getAsks().size(),
                            new Date(snapshot.getTimestamp()));
                })
                .print("OrderBook")
                .name("Order Book Console Output");

        // 输出盈亏结果到控制台
        pnlStream
                .map(pnl -> {
                    return String.format("[PnL] Member: %s, Total PnL: %.2f, Date: %s, Timestamp: %s",
                            pnl.getSettle_memb_unfy_cde(),
                            pnl.getTotal_pnl(),
                            pnl.getDate(),
                            new Date(pnl.getTimestamp()));
                })
                .print("PnL")
                .name("PnL Console Output");

        // 输出统计信息
        singleLegStream.map(order -> "SingleLeg: " + order.getContract_cde()).print("SingleLegCount");
        combStream.map(order -> "Combination: " + order.getLeg_1_contract_cde() + "-" + order.getLeg_2_contract_cde()).print("CombCount");
        tradeStream.map(trade -> "Trade: " + trade.getContract_cde()).print("TradeCount");

        // 启动作业
        System.out.println("Starting Futures Data Reconstruction Job (Local File Version)...");
        env.execute("Futures Data Reconstruction Job - Local File Version");
    }

    // ============ 辅助方法 ============

    public static SingleLegOrderEvent parseSingleLegOrder(String jsonStr) throws Exception {
        Map<String, Object> jsonMap = objectMapper.readValue(jsonStr, Map.class);

        SingleLegOrderEvent event = new SingleLegOrderEvent();
        event.setOrd_nbr(getString(jsonMap, "ord_nbr"));
        event.setContract_cde(getString(jsonMap, "contract_cde"));
        event.setB_s_tag(getString(jsonMap, "b_s_tag"));
        event.setOrd_prc(getDouble(jsonMap, "ord_prc"));
        event.setOrd_vol(getLong(jsonMap, "ord_vol"));
        event.setRmn_vol(getLong(jsonMap, "rmn_vol"));
        event.setOrd_sts(getString(jsonMap, "ord_sts"));

        // 解析时间戳
        String ordDt = getString(jsonMap, "ord_dt");
        String ordTm = getString(jsonMap, "ord_tm");
        event.setEventTimestamp(parseTimestamp(ordDt, ordTm));

        // 添加数据验证
        OrderValidator.ValidationResult validation = OrderValidator.validateSingleLegOrder(event);
        if (!validation.isValid()) {
            System.err.println("单腿订单验证失败: " + validation.getErrorMessage() + ", 数据: " + jsonStr);
            // 对于本地测试，我们记录错误但不抛出异常
        }

        return event;
    }

    public static CombinationOrderEvent parseCombinationOrder(String jsonStr) throws Exception {
        Map<String, Object> jsonMap = objectMapper.readValue(jsonStr, Map.class);

        CombinationOrderEvent event = new CombinationOrderEvent();
        event.setOrd_nbr(getString(jsonMap, "ord_nbr"));
        event.setLeg_1_contract_cde(getString(jsonMap, "leg_1_contract_cde"));
        event.setLeg_2_contract_cde(getString(jsonMap, "leg_2_contract_cde"));
        event.setB_s_tag(getString(jsonMap, "b_s_tag"));
        event.setOrd_prc(getDouble(jsonMap, "ord_prc"));
        event.setOrd_vol(getLong(jsonMap, "ord_vol"));
        event.setRmn_vol(getLong(jsonMap, "rmn_vol"));
        event.setOrd_sts(getString(jsonMap, "ord_sts"));

        // 解析时间戳
        String ordDt = getString(jsonMap, "ord_dt");
        String ordTm = getString(jsonMap, "ord_tm");
        event.setEventTimestamp(parseTimestamp(ordDt, ordTm));

        // 添加数据验证
        OrderValidator.ValidationResult validation = OrderValidator.validateCombinationOrder(event);
        if (!validation.isValid()) {
            System.err.println("组合订单验证失败: " + validation.getErrorMessage() + ", 数据: " + jsonStr);
            // 对于本地测试，我们记录错误但不抛出异常
        }

        return event;
    }

    public static TradeEvent parseTradeEvent(String jsonStr) throws Exception {
        Map<String, Object> jsonMap = objectMapper.readValue(jsonStr, Map.class);

        TradeEvent event = new TradeEvent();
        event.setTrd_dt(getString(jsonMap, "trd_dt"));
        event.setOrd_nbr(getString(jsonMap, "ord_nbr"));
        event.setContract_cde(getString(jsonMap, "contract_cde"));
        event.setB_s_tag(getString(jsonMap, "b_s_tag"));
        event.setTrd_prc(getDouble(jsonMap, "trd_prc"));
        event.setTrd_vol(getLong(jsonMap, "trd_vol"));
        event.setTrd_type(getString(jsonMap, "trd_type"));
        event.setSettle_memb_unfy_cde(getString(jsonMap, "settle_memb_unfy_cde"));
        event.setOcpos_type(getString(jsonMap, "ocpos_type"));

        // 解析时间戳
        String trdDt = getString(jsonMap, "trd_dt");
        String trdTm = getString(jsonMap, "trd_tm");
        event.setEventTimestamp(parseTimestamp(trdDt, trdTm));

        // 添加数据验证
        OrderValidator.ValidationResult validation = OrderValidator.validateTradeEvent(event);
        if (!validation.isValid()) {
            System.err.println("成交事件验证失败: " + validation.getErrorMessage() + ", 数据: " + jsonStr);
            // 对于本地测试，我们记录错误但不抛出异常
        }

        return event;
    }

    private static String getString(Map<String, Object> map, String key) {
        Object value = map.get(key);
        return value != null ? value.toString() : "";
    }

    private static double getDouble(Map<String, Object> map, String key) {
        Object value = map.get(key);
        if (value == null) return 0.0;
        if (value instanceof Number) {
            return ((Number) value).doubleValue();
        }
        try {
            return Double.parseDouble(value.toString());
        } catch (NumberFormatException e) {
            return 0.0;
        }
    }

    private static long getLong(Map<String, Object> map, String key) {
        Object value = map.get(key);
        if (value == null) return 0L;
        if (value instanceof Number) {
            return ((Number) value).longValue();
        }
        try {
            return Long.parseLong(value.toString());
        } catch (NumberFormatException e) {
            return 0L;
        }
    }

    private static long parseTimestamp(String date, String time) {
        try {
            // 处理时间格式：2025-03-10 和 14:27:44
            if (date == null || time == null || date.isEmpty() || time.isEmpty()) {
                return System.currentTimeMillis();
            }

            // 组合日期和时间
            String dateTimeStr = date + " " + time;
            DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
            LocalDateTime dateTime = LocalDateTime.parse(dateTimeStr, formatter);

            // 转换为时间戳（毫秒）
            return dateTime.atZone(java.time.ZoneId.systemDefault()).toInstant().toEpochMilli();
        } catch (Exception e) {
            System.err.println("时间戳解析失败: " + date + " " + time + ", 错误: " + e.getMessage());
            return System.currentTimeMillis();
        }
    }
}
