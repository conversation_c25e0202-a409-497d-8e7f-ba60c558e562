package com.futures.job;

import org.apache.flink.streaming.api.datastream.DataStream;
import org.apache.flink.streaming.api.environment.StreamExecutionEnvironment;

/**
 * 最简单的数据读取测试
 */
public class DataReadTest {

    public static void main(String[] args) throws Exception {
        System.out.println("========================================");
        System.out.println("数据读取测试");
        System.out.println("========================================");

        // 创建Flink执行环境
        StreamExecutionEnvironment env = StreamExecutionEnvironment.getExecutionEnvironment();
        env.setParallelism(1);

        System.out.println("正在读取单腿订单数据...");

        // 读取单腿订单数据
        DataStream<String> singleLegStream = env.readTextFile("data/mock_single_leg_orders_final.txt");

        // 输出前10条数据
        singleLegStream
                .map(line -> "SingleLeg: " + line)
                .print("SingleLegData");

        System.out.println("正在读取成交数据...");

        // 读取成交数据
        DataStream<String> tradeStream = env.readTextFile("data/mock_trade_records_final.txt");

        // 输出前10条数据
        tradeStream
                .map(line -> "Trade: " + line)
                .print("TradeData");

        System.out.println("启动Flink作业...");

        // 启动作业
        env.execute("Data Read Test");
    }
}
