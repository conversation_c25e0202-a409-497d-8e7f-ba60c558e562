package com.futures.job;

// ============ 3. 主应用程序 ============

import com.futures.datagen.model.*;
import com.futures.function.OrderBookReconstructionFunction;
import com.futures.function.PnLCalculationFunction;
import com.futures.function.CombinationOrderSplitter;
import com.futures.validation.OrderValidator;
import org.apache.flink.api.common.eventtime.WatermarkStrategy;
import org.apache.flink.api.common.serialization.SimpleStringSchema;
import org.apache.flink.connector.kafka.source.KafkaSource;
import org.apache.flink.streaming.api.datastream.DataStream;
import org.apache.flink.streaming.api.datastream.SingleOutputStreamOperator;
import org.apache.flink.streaming.api.environment.StreamExecutionEnvironment;
import org.apache.flink.streaming.connectors.kafka.FlinkKafkaProducer;
import org.apache.flink.api.common.functions.MapFunction;
import org.apache.flink.shaded.jackson2.com.fasterxml.jackson.databind.ObjectMapper;
import org.apache.flink.contrib.streaming.state.RocksDBStateBackend;
import org.apache.flink.streaming.api.CheckpointingMode;

import java.text.SimpleDateFormat;
import java.time.Duration;
import java.util.*;

public class FuturesDataReconstructionJob {

    private static final ObjectMapper objectMapper = new ObjectMapper();

    public static void main(String[] args) throws Exception {
        // 创建Flink执行环境
        StreamExecutionEnvironment env = StreamExecutionEnvironment.getExecutionEnvironment();
        env.setParallelism(4);

        // 配置RocksDB状态后端以防止内存泄漏
        try {
            env.setStateBackend(new RocksDBStateBackend("file:///tmp/flink-checkpoints", true));
        } catch (Exception e) {
            throw new RuntimeException("Failed to set RocksDB state backend", e);
        }

        // 配置检查点
        env.enableCheckpointing(5000);
        env.getCheckpointConfig().setCheckpointingMode(CheckpointingMode.EXACTLY_ONCE);
        env.getCheckpointConfig().setMinPauseBetweenCheckpoints(500);
        env.getCheckpointConfig().setCheckpointTimeout(60000);
        env.getCheckpointConfig().setMaxConcurrentCheckpoints(1);

        // ============ 数据源配置 ============

        // 单腿委托数据源
        KafkaSource<String> singleLegSource = KafkaSource.<String>builder()
                .setBootstrapServers("localhost:9092")
                .setTopics("singleleg-orders")
                .setValueOnlyDeserializer(new SimpleStringSchema())
                .build();

        DataStream<String> singleLegStringStream = env.fromSource(singleLegSource,
                WatermarkStrategy.<String>forBoundedOutOfOrderness(Duration.ofSeconds(3))
                        .withTimestampAssigner((element, recordTimestamp) -> {
                            try {
                                // 从JSON中提取事件时间戳
                                Map<String, Object> jsonMap = objectMapper.readValue(element, Map.class);
                                String ordDt = getString(jsonMap, "ord_dt");
                                String ordTm = getString(jsonMap, "ord_tm");
                                return parseTimestamp(ordDt, ordTm);
                            } catch (Exception e) {
                                // 如果解析失败，使用记录时间戳作为后备
                                return recordTimestamp;
                            }
                        }),
                "Single Leg Orders Source");

        // 组合委托数据源
        KafkaSource<String> combSource = KafkaSource.<String>builder()
                .setBootstrapServers("localhost:9092")
                .setTopics("combination-orders")
                .setValueOnlyDeserializer(new SimpleStringSchema())
                .build();

        DataStream<String> combStringStream = env.fromSource(combSource,
                WatermarkStrategy.<String>forBoundedOutOfOrderness(Duration.ofSeconds(3))
                        .withTimestampAssigner((element, recordTimestamp) -> {
                            try {
                                // 从JSON中提取事件时间戳
                                Map<String, Object> jsonMap = objectMapper.readValue(element, Map.class);
                                String ordDt = getString(jsonMap, "ord_dt");
                                String ordTm = getString(jsonMap, "ord_tm");
                                return parseTimestamp(ordDt, ordTm);
                            } catch (Exception e) {
                                // 如果解析失败，使用记录时间戳作为后备
                                return recordTimestamp;
                            }
                        }),
                "Combination Orders Source");

        // 成交数据源
        KafkaSource<String> tradeSource = KafkaSource.<String>builder()
                .setBootstrapServers("localhost:9092")
                .setTopics("trades")
                .setValueOnlyDeserializer(new SimpleStringSchema())
                .build();

        DataStream<String> tradeStringStream = env.fromSource(tradeSource,
                WatermarkStrategy.<String>forBoundedOutOfOrderness(Duration.ofSeconds(3))
                        .withTimestampAssigner((element, recordTimestamp) -> {
                            try {
                                // 从JSON中提取事件时间戳
                                Map<String, Object> jsonMap = objectMapper.readValue(element, Map.class);
                                String trdDt = getString(jsonMap, "trd_dt");
                                String trdTm = getString(jsonMap, "trd_tm");
                                return parseTimestamp(trdDt, trdTm);
                            } catch (Exception e) {
                                // 如果解析失败，使用记录时间戳作为后备
                                return recordTimestamp;
                            }
                        }),
                "Trades Source");

        // ============ 数据解析 ============

        // 解析单腿委托
        DataStream<SingleLegOrderEvent> singleLegStream = singleLegStringStream
                .map(new MapFunction<String, SingleLegOrderEvent>() {
                    @Override
                    public SingleLegOrderEvent map(String jsonStr) throws Exception {
                        return parseSingleLegOrder(jsonStr);
                    }
                });

        // 解析组合委托
        DataStream<CombinationOrderEvent> combStream = combStringStream
                .map(new MapFunction<String, CombinationOrderEvent>() {
                    @Override
                    public CombinationOrderEvent map(String jsonStr) throws Exception {
                        return parseCombinationOrder(jsonStr);
                    }
                });

        // 解析成交数据
        DataStream<TradeEvent> tradeStream = tradeStringStream
                .map(new MapFunction<String, TradeEvent>() {
                    @Override
                    public TradeEvent map(String jsonStr) throws Exception {
                        return parseTradeEvent(jsonStr);
                    }
                });

        // ============ 订单簿重建流处理 ============

        // 处理组合订单分发
        SingleOutputStreamOperator<Object> combOrderSplitterStream = combStream
                .process(new CombinationOrderSplitter());

        // 获取分发后的两条腿数据流
        DataStream<CombinationOrderSplitter.CombinationOrderLegInfo> leg1Stream = combOrderSplitterStream
                .getSideOutput(CombinationOrderSplitter.LEG1_OUTPUT_TAG);
        DataStream<CombinationOrderSplitter.CombinationOrderLegInfo> leg2Stream = combOrderSplitterStream
                .getSideOutput(CombinationOrderSplitter.LEG2_OUTPUT_TAG);

        // 为第一条腿构建订单簿
        DataStream<OrderBookSnapshot> leg1OrderBookStream = singleLegStream
                .map(x -> (Object) x)
                .union(leg1Stream.map(x -> (Object) x))
                .keyBy(order -> {
                    if (order instanceof SingleLegOrderEvent) {
                        return ((SingleLegOrderEvent) order).getContract_cde();
                    } else if (order instanceof CombinationOrderSplitter.CombinationOrderLegInfo) {
                        return ((CombinationOrderSplitter.CombinationOrderLegInfo) order).getCurrentLegContract();
                    } else {
                        return "unknown";
                    }
                })
                .process(new OrderBookReconstructionFunction());

        // 为第二条腿构建订单簿
        DataStream<OrderBookSnapshot> leg2OrderBookStream = singleLegStream
                .map(x -> (Object) x)
                .union(leg2Stream.map(x -> (Object) x))
                .keyBy(order -> {
                    if (order instanceof SingleLegOrderEvent) {
                        return ((SingleLegOrderEvent) order).getContract_cde();
                    } else if (order instanceof CombinationOrderSplitter.CombinationOrderLegInfo) {
                        return ((CombinationOrderSplitter.CombinationOrderLegInfo) order).getCurrentLegContract();
                    } else {
                        return "unknown";
                    }
                })
                .process(new OrderBookReconstructionFunction());

        // 合并两条腿的订单簿快照
        DataStream<OrderBookSnapshot> orderBookStream = leg1OrderBookStream.union(leg2OrderBookStream);

        // ============ 盈亏计算流处理 ============

        // 按结算会员分区并计算盈亏
        DataStream<PnLResult> pnlStream = tradeStream
                .keyBy(TradeEvent::getSettle_memb_unfy_cde)
                .process(new PnLCalculationFunction());

        // ============ 数据输出 ============

        // 输出订单簿快照到Kafka
        FlinkKafkaProducer<String> orderBookSink = new FlinkKafkaProducer<>(
                "orderbook-snapshots",
                new SimpleStringSchema(),
                getKafkaProducerConfig()
        );

        orderBookStream
                .map(snapshot -> serializeOrderBookSnapshot(snapshot))
                .addSink(orderBookSink)
                .name("Order Book Sink");

        // 输出盈亏结果到Kafka
        FlinkKafkaProducer<String> pnlSink = new FlinkKafkaProducer<>(
                "pnl-results",
                new SimpleStringSchema(),
                getKafkaProducerConfig()
        );

        pnlStream
                .map(pnl -> serializePnLResult(pnl))
                .addSink(pnlSink)
                .name("PnL Results Sink");

        // 启动作业
        env.execute("Futures Data Reconstruction Job");
    }

    // ============ 辅助方法 ============

    public static SingleLegOrderEvent parseSingleLegOrder(String jsonStr) throws Exception {
        Map<String, Object> jsonMap = objectMapper.readValue(jsonStr, Map.class);

        SingleLegOrderEvent event = new SingleLegOrderEvent();
        event.setOrd_nbr(getString(jsonMap, "ord_nbr"));
        event.setContract_cde(getString(jsonMap, "contract_cde"));
        event.setB_s_tag(getString(jsonMap, "b_s_tag"));
        event.setOrd_prc(getDouble(jsonMap, "ord_prc"));
        event.setOrd_vol(getLong(jsonMap, "ord_vol"));
        event.setRmn_vol(getLong(jsonMap, "rmn_vol"));
        event.setOrd_sts(getString(jsonMap, "ord_sts"));

        // 解析时间戳
        String ordDt = getString(jsonMap, "ord_dt");
        String ordTm = getString(jsonMap, "ord_tm");
        event.setEventTimestamp(parseTimestamp(ordDt, ordTm));

        // 添加数据验证
        OrderValidator.ValidationResult validation = OrderValidator.validateSingleLegOrder(event);
        if (!validation.isValid()) {
            throw new IllegalArgumentException("单腿订单验证失败: " + validation.getErrorMessage());
        }

        return event;
    }

    public static CombinationOrderEvent parseCombinationOrder(String jsonStr) throws Exception {
        Map<String, Object> jsonMap = objectMapper.readValue(jsonStr, Map.class);

        CombinationOrderEvent event = new CombinationOrderEvent();
        event.setOrd_nbr(getString(jsonMap, "ord_nbr"));
        event.setLeg_1_contract_cde(getString(jsonMap, "leg_1_contract_cde"));
        event.setLeg_2_contract_cde(getString(jsonMap, "leg_2_contract_cde"));
        event.setB_s_tag(getString(jsonMap, "b_s_tag"));
        event.setOrd_prc(getDouble(jsonMap, "ord_prc"));
        event.setOrd_vol(getLong(jsonMap, "ord_vol"));
        event.setRmn_vol(getLong(jsonMap, "rmn_vol"));
        event.setOrd_sts(getString(jsonMap, "ord_sts"));

        String ordDt = getString(jsonMap, "ord_dt");
        String ordTm = getString(jsonMap, "ord_tm");
        event.setEventTimestamp(parseTimestamp(ordDt, ordTm));

        return event;
    }

    private static TradeEvent parseTradeEvent(String jsonStr) throws Exception {
        Map<String, Object> jsonMap = objectMapper.readValue(jsonStr, Map.class);

        TradeEvent event = new TradeEvent();
        event.setTrd_dt(getString(jsonMap, "trd_dt"));
        event.setOrd_nbr(getString(jsonMap, "ord_nbr"));
        event.setContract_cde(getString(jsonMap, "contract_cde"));
        event.setB_s_tag(getString(jsonMap, "b_s_tag"));
        event.setTrd_prc(getDouble(jsonMap, "trd_prc"));
        event.setTrd_vol(getLong(jsonMap, "trd_vol"));
        event.setTrd_type(getString(jsonMap, "trd_type"));
        event.setSettle_memb_unfy_cde(getString(jsonMap, "settle_memb_unfy_cde"));
        event.setOcpos_type(getString(jsonMap, "ocpos_type"));

        String trdDt = getString(jsonMap, "trd_dt");
        String trdTm = getString(jsonMap, "trd_tm");
        event.setEventTimestamp(parseTimestamp(trdDt, trdTm));

        return event;
    }

    private static String serializeOrderBookSnapshot(OrderBookSnapshot snapshot) throws Exception {
        Map<String, Object> result = new HashMap<>();
        result.put("contract_code", snapshot.getContract_cde());
        result.put("timestamp", snapshot.getTimestamp());
        result.put("best_bid", snapshot.getBestBid());
        result.put("best_ask", snapshot.getBestAsk());

        // 序列化买单簿（只保留前5档）
        List<Map<String, Object>> bidsList = new ArrayList<>();
        int bidCount = 0;
        for (Map.Entry<Double, Long> entry : snapshot.getBids().entrySet()) {
            if (bidCount >= 5) break;
            if (entry.getValue() > 0) {
                Map<String, Object> level = new HashMap<>();
                level.put("price", entry.getKey());
                level.put("volume", entry.getValue());
                bidsList.add(level);
                bidCount++;
            }
        }
        result.put("bids", bidsList);

        // 序列化卖单簿（只保留前5档）
        List<Map<String, Object>> asksList = new ArrayList<>();
        int askCount = 0;
        for (Map.Entry<Double, Long> entry : snapshot.getAsks().entrySet()) {
            if (askCount >= 5) break;
            if (entry.getValue() > 0) {
                Map<String, Object> level = new HashMap<>();
                level.put("price", entry.getKey());
                level.put("volume", entry.getValue());
                asksList.add(level);
                askCount++;
            }
        }
        result.put("asks", asksList);

        return objectMapper.writeValueAsString(result);
    }

    private static String serializePnLResult(PnLResult pnl) throws Exception {
        Map<String, Object> result = new HashMap<>();
        result.put("member_code", pnl.getSettle_memb_unfy_cde());
        result.put("total_pnl", pnl.getTotal_pnl());
        result.put("date", pnl.getDate());
        result.put("timestamp", pnl.getTimestamp());
        return objectMapper.writeValueAsString(result);
    }

    private static Properties getKafkaProducerConfig() {
        Properties props = new Properties();
        props.setProperty("bootstrap.servers", "localhost:9092");
        props.setProperty("key.serializer", "org.apache.kafka.common.serialization.StringSerializer");
        props.setProperty("value.serializer", "org.apache.kafka.common.serialization.StringSerializer");
        return props;
    }

    // JSON解析辅助方法
    private static String getString(Map<String, Object> map, String key) {
        Object value = map.get(key);
        return value != null ? value.toString() : "";
    }

    private static double getDouble(Map<String, Object> map, String key) {
        Object value = map.get(key);
        if (value instanceof Number) {
            return ((Number) value).doubleValue();
        }
        try {
            return Double.parseDouble(value.toString());
        } catch (Exception e) {
            return 0.0;
        }
    }

    private static long getLong(Map<String, Object> map, String key) {
        Object value = map.get(key);
        if (value instanceof Number) {
            return ((Number) value).longValue();
        }
        try {
            return Long.parseLong(value.toString());
        } catch (Exception e) {
            return 0L;
        }
    }

    private static long parseTimestamp(String date, String time) {
        try {
            // 简化的时间戳解析，实际项目中应该使用更robust的日期解析
            String dateTimeStr = date + " " + time;
            SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
            Date parsedDate = sdf.parse(dateTimeStr);
            return parsedDate.getTime();
        } catch (Exception e) {
            return System.currentTimeMillis();
        }
    }
}
