package com.futures.function;

import com.futures.datagen.model.PnLResult;
import com.futures.datagen.model.Position;
import com.futures.datagen.model.TradeEvent;
import org.apache.flink.api.common.state.*;
import org.apache.flink.configuration.Configuration;
import org.apache.flink.streaming.api.functions.KeyedProcessFunction;
import org.apache.flink.util.Collector;
import java.util.*;
// 盈亏计算算子
public class PnLCalculationFunction extends KeyedProcessFunction<String, TradeEvent, PnLResult> {

    private transient ValueState<Double> totalPnL;
    private transient ListState<Position> openPositions;

    @Override
    public void open(Configuration parameters) throws Exception {
        ValueStateDescriptor<Double> pnlDescriptor =
                new ValueStateDescriptor<>("total-pnl", Double.class, 0.0);
        totalPnL = getRuntimeContext().getState(pnlDescriptor);

        ListStateDescriptor<Position> positionsDescriptor =
                new ListStateDescriptor<>("open-positions", Position.class);
        openPositions = getRuntimeContext().getListState(positionsDescriptor);
    }

    @Override
    public void processElement(TradeEvent trade, Context ctx, Collector<PnLResult> out) throws Exception {
        String ocposType = trade.getOcpos_type();
        String contractCode = trade.getContract_cde();
        String buySellTag = trade.getB_s_tag();
        double tradePrice = trade.getTrd_prc();
        long tradeVolume = trade.getTrd_vol();

        if ("0".equals(ocposType)) {
            // 开仓：添加到持仓列表
            Position newPosition = new Position(contractCode, buySellTag, tradePrice, tradeVolume);
            openPositions.add(newPosition);
        } else if ("1".equals(ocposType) || "3".equals(ocposType) || "4".equals(ocposType)) {
            // 平仓：计算盈亏
            double pnl = calculateClosingPnL(trade);
            double currentPnL = totalPnL.value();
            totalPnL.update(currentPnL + pnl);

            // 输出当前累计盈亏
            String memberCode = trade.getSettle_memb_unfy_cde();
            String date = trade.getTrd_dt();
            out.collect(new PnLResult(memberCode, currentPnL + pnl, date));
        }
    }

    private double calculateClosingPnL(TradeEvent closingTrade) throws Exception {
        String contractCode = closingTrade.getContract_cde();
        String closingDirection = closingTrade.getB_s_tag();
        double closingPrice = closingTrade.getTrd_prc();
        long closingVolume = closingTrade.getTrd_vol();

        double totalPnl = 0.0;
        List<Position> updatedPositions = new ArrayList<>();
        long remainingVolume = closingVolume;

        // FIFO匹配持仓
        for (Position position : openPositions.get()) {
            if (!position.getContract_cde().equals(contractCode)) {
                updatedPositions.add(position);
                continue;
            }

            // 检查是否为相反方向的持仓
            boolean isOppositeDirection =
                    ("B".equals(closingDirection) && "S".equals(position.getB_s_tag())) ||
                            ("S".equals(closingDirection) && "B".equals(position.getB_s_tag()));

            if (!isOppositeDirection) {
                updatedPositions.add(position);
                continue;
            }

            if (remainingVolume <= 0) {
                updatedPositions.add(position);
                continue;
            }

            long matchedVolume = Math.min(remainingVolume, position.getVolume());

            // 计算盈亏：(平仓价 - 开仓价) * 数量 * 方向
            double pnl;
            if ("B".equals(closingDirection)) {
                // 买入平仓（原来是卖出开仓）
                pnl = (position.getOpen_price() - closingPrice) * matchedVolume;
            } else {
                // 卖出平仓（原来是买入开仓）
                pnl = (closingPrice - position.getOpen_price()) * matchedVolume;
            }

            totalPnl += pnl;
            remainingVolume -= matchedVolume;

            // 更新持仓数量
            if (matchedVolume < position.getVolume()) {
                position.setVolume(position.getVolume() - matchedVolume);
                updatedPositions.add(position);
            }
            // 如果完全平仓，则不添加到updatedPositions中
        }

        // 更新持仓列表
        openPositions.clear();
        openPositions.addAll(updatedPositions);

        return totalPnl;
    }
}
