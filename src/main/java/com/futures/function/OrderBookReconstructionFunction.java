package com.futures.function;

// ============ 2. 核心处理算子 ============

import com.futures.datagen.model.CombinationOrderEvent;
import com.futures.datagen.model.OrderBookSnapshot;
import com.futures.datagen.model.SingleLegOrderEvent;
import com.futures.function.CombinationOrderSplitter.CombinationOrderLegInfo;
import org.apache.flink.api.common.state.*;
import org.apache.flink.api.common.time.Time;
import org.apache.flink.configuration.Configuration;
import org.apache.flink.streaming.api.functions.KeyedProcessFunction;
import org.apache.flink.util.Collector;
import java.util.*;

// 订单簿重建算子 - 支持单腿订单和组合订单腿信息处理
public class OrderBookReconstructionFunction extends KeyedProcessFunction<String, Object, OrderBookSnapshot> {

    private transient MapState<Double, Long> baseBids;
    private transient MapState<Double, Long> baseAsks;
    private transient MapState<String, CombinationOrderLegInfo> activeCombOrders; // 优化：使用MapState
    private transient ValueState<Double> bestBidPrice;
    private transient ValueState<Double> bestAskPrice;
    private transient ValueState<Boolean> timerRegistered; // 防止重复注册定时器

    private static final long SNAPSHOT_INTERVAL_MS = 500; // 0.5秒快照间隔

    @Override
    public void open(Configuration parameters) throws Exception {
        // 配置TTL以防止内存泄漏
        StateTtlConfig ttlConfig = StateTtlConfig
                .newBuilder(Time.hours(24))
                .setUpdateType(StateTtlConfig.UpdateType.OnCreateAndWrite)
                .setStateVisibility(StateTtlConfig.StateVisibility.NeverReturnExpired)
                .build();

        MapStateDescriptor<Double, Long> bidsDescriptor =
                new MapStateDescriptor<>("base-bids", Double.class, Long.class);
        bidsDescriptor.enableTimeToLive(ttlConfig);
        baseBids = getRuntimeContext().getMapState(bidsDescriptor);

        MapStateDescriptor<Double, Long> asksDescriptor =
                new MapStateDescriptor<>("base-asks", Double.class, Long.class);
        asksDescriptor.enableTimeToLive(ttlConfig);
        baseAsks = getRuntimeContext().getMapState(asksDescriptor);

        // 优化：使用MapState替代ListState，提高性能
        MapStateDescriptor<String, CombinationOrderLegInfo> combOrdersDescriptor =
                new MapStateDescriptor<>("active-comb-orders", String.class, CombinationOrderLegInfo.class);
        combOrdersDescriptor.enableTimeToLive(ttlConfig);
        activeCombOrders = getRuntimeContext().getMapState(combOrdersDescriptor);

        ValueStateDescriptor<Double> bestBidDescriptor =
                new ValueStateDescriptor<>("best-bid-price", Double.class);
        bestBidDescriptor.enableTimeToLive(ttlConfig);
        bestBidPrice = getRuntimeContext().getState(bestBidDescriptor);

        ValueStateDescriptor<Double> bestAskDescriptor =
                new ValueStateDescriptor<>("best-ask-price", Double.class);
        bestAskDescriptor.enableTimeToLive(ttlConfig);
        bestAskPrice = getRuntimeContext().getState(bestAskDescriptor);

        ValueStateDescriptor<Boolean> timerDescriptor =
                new ValueStateDescriptor<>("timer-registered", Boolean.class, false);
        timerRegistered = getRuntimeContext().getState(timerDescriptor);
    }

    @Override
    public void processElement(Object value, Context ctx, Collector<OrderBookSnapshot> out) throws Exception {
        if (value instanceof SingleLegOrderEvent) {
            processSingleLegOrder((SingleLegOrderEvent) value, ctx);
        } else if (value instanceof CombinationOrderLegInfo) {
            processCombinationOrderLeg((CombinationOrderLegInfo) value, ctx);
        }

        // 优化：只注册一次定时器
        if (!timerRegistered.value()) {
            long currentTime = ctx.timerService().currentProcessingTime();
            long nextSnapshotTime = ((currentTime / SNAPSHOT_INTERVAL_MS) + 1) * SNAPSHOT_INTERVAL_MS;
            ctx.timerService().registerProcessingTimeTimer(nextSnapshotTime);
            timerRegistered.update(true);
        }
    }

    private void processSingleLegOrder(SingleLegOrderEvent order, Context ctx) throws Exception {
        String contractCode = order.getContract_cde();
        double price = order.getOrd_prc();
        long remainingVol = order.getRmn_vol();
        String orderStatus = order.getOrd_sts();
        String buySellTag = order.getB_s_tag();
        String orderNumber = order.getOrd_nbr();

        // 根据订单状态和剩余数量更新订单簿
        if ("5".equals(orderStatus) || remainingVol == 0) {
            // 订单已撤销或完全成交，需要从订单簿中减去该订单的数量
            // 注意：这里需要知道原订单的数量，简化处理是移除整个价格档位
            // 实际生产环境中应该维护订单ID到数量的映射
            if ("B".equals(buySellTag)) {
                Long currentVol = baseBids.get(price);
                if (currentVol != null) {
                    // 简化处理：如果是撤销，移除整个价格档位
                    // 生产环境中应该只减去该订单的数量
                    baseBids.remove(price);
                }
            } else {
                Long currentVol = baseAsks.get(price);
                if (currentVol != null) {
                    baseAsks.remove(price);
                }
            }
        } else {
            // 更新订单簿 - 修复：使用聚合而不是覆盖
            if ("B".equals(buySellTag)) {
                if (remainingVol > 0) {
                    Long currentVol = baseBids.get(price);
                    // 聚合同价格档位的数量
                    baseBids.put(price, (currentVol != null ? currentVol : 0L) + remainingVol);
                }
            } else {
                if (remainingVol > 0) {
                    Long currentVol = baseAsks.get(price);
                    // 聚合同价格档位的数量
                    baseAsks.put(price, (currentVol != null ? currentVol : 0L) + remainingVol);
                }
            }
        }

        // 更新最优价格缓存
        updateBestPrices();
    }

    /**
     * 处理组合订单腿信息
     * @param legInfo 组合订单腿信息
     * @param ctx 处理上下文
     */
    private void processCombinationOrderLeg(CombinationOrderLegInfo legInfo, Context ctx) throws Exception {
        String orderStatus = legInfo.getOrderStatus();
        String orderKey = legInfo.getOrdNbr() + "_" + legInfo.getLegNumber();

        if ("5".equals(orderStatus) || legInfo.getRemainingVol() == 0) {
            // 移除已撤销或完成的组合订单腿 - 优化：O(1)操作
            activeCombOrders.remove(orderKey);
        } else {
            // 更新或添加活跃的组合订单腿 - 优化：O(1)操作
            activeCombOrders.put(orderKey, legInfo);
        }
    }

    private void updateBestPrices() throws Exception {
        Double bestBid = getBestPrice(baseBids, true);
        Double bestAsk = getBestPrice(baseAsks, false);

        bestBidPrice.update(bestBid);
        bestAskPrice.update(bestAsk);
    }

    private Double getBestPrice(MapState<Double, Long> priceMap, boolean isBid) throws Exception {
        List<Double> prices = new ArrayList<>();
        for (Map.Entry<Double, Long> entry : priceMap.entries()) {
            if (entry.getValue() > 0) {
                prices.add(entry.getKey());
            }
        }

        if (prices.isEmpty()) return null;

        if (isBid) {
            return Collections.max(prices); // 买单取最高价
        } else {
            return Collections.min(prices); // 卖单取最低价
        }
    }

    @Override
    public void onTimer(long timestamp, OnTimerContext ctx, Collector<OrderBookSnapshot> out) throws Exception {
        String contractCode = ctx.getCurrentKey();

        // 1. 创建基础层快照
        OrderBookSnapshot snapshot = new OrderBookSnapshot(contractCode);

        // 复制基础买单簿
        for (Map.Entry<Double, Long> entry : baseBids.entries()) {
            if (entry.getValue() > 0) {
                snapshot.getBids().put(entry.getKey(), entry.getValue());
            }
        }

        // 复制基础卖单簿
        for (Map.Entry<Double, Long> entry : baseAsks.entries()) {
            if (entry.getValue() > 0) {
                snapshot.getAsks().put(entry.getKey(), entry.getValue());
            }
        }

        // 2. 计算并叠加虚拟层
        for (CombinationOrderLegInfo legInfo : activeCombOrders.values()) {
            if (legInfo.getRemainingVol() > 0) {
                addVirtualOrdersToSnapshot(snapshot, legInfo);
            }
        }

        snapshot.setTimestamp(timestamp);
        out.collect(snapshot);

        // 注册下一个定时器
        ctx.timerService().registerProcessingTimeTimer(timestamp + SNAPSHOT_INTERVAL_MS);
        timerRegistered.update(false); // 重置定时器注册状态
    }

    /**
     * 为订单簿快照添加虚拟订单
     * 修正了价差计算公式的数学错误
     *
     * @param snapshot 订单簿快照
     * @param legInfo 组合订单腿信息
     */
    private void addVirtualOrdersToSnapshot(OrderBookSnapshot snapshot, CombinationOrderLegInfo legInfo) throws Exception {
        String buySellTag = legInfo.getBuySellTag();
        double spreadPrice = legInfo.getSpreadPrice();
        long volume = legInfo.getRemainingVol();

        // 获取当前合约的最优价格（用于计算虚拟订单）
        Double currentBestBid = bestBidPrice.value();
        Double currentBestAsk = bestAskPrice.value();

        // 修正的价差计算逻辑
        // 对于价差订单：Spread = Leg1_Price - Leg2_Price
        // 当前处理的是其中一条腿，需要根据价差和对手腿价格计算虚拟订单价格

        if ("B".equals(buySellTag)) { // 买入当前腿
            // 买入当前腿时，虚拟订单价格基于当前最优卖价
            if (currentBestAsk != null) {
                // 虚拟买单价格 = 当前最优卖价 - 一个最小价格单位（简化处理）
                double virtualPrice = currentBestAsk - 0.01; // 简化处理，实际应该使用最小价格变动单位
                addVirtualOrder(snapshot, virtualPrice, volume, true);
            }
        } else { // 卖出当前腿
            // 卖出当前腿时，虚拟订单价格基于当前最优买价
            if (currentBestBid != null) {
                // 虚拟卖单价格 = 当前最优买价 + 一个最小价格单位（简化处理）
                double virtualPrice = currentBestBid + 0.01; // 简化处理，实际应该使用最小价格变动单位
                addVirtualOrder(snapshot, virtualPrice, volume, false);
            }
        }
    }

    private void addVirtualOrder(OrderBookSnapshot snapshot, double price, long volume, boolean isBid) {
        if (isBid) {
            snapshot.getBids().merge(price, volume, Long::sum);
        } else {
            snapshot.getAsks().merge(price, volume, Long::sum);
        }
    }
}
