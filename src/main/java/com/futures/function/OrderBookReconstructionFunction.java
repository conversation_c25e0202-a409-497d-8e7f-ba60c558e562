package com.futures.function;

// ============ 2. 核心处理算子 ============

import com.futures.datagen.model.CombinationOrderEvent;
import com.futures.datagen.model.OrderBookSnapshot;
import com.futures.datagen.model.SingleLegOrderEvent;
import org.apache.flink.api.common.state.*;
import org.apache.flink.configuration.Configuration;
import org.apache.flink.streaming.api.functions.KeyedProcessFunction;
import org.apache.flink.util.Collector;
import java.util.*;

// 订单簿重建算子
public class OrderBookReconstructionFunction extends KeyedProcessFunction<String, Object, OrderBookSnapshot> {

    private transient MapState<Double, Long> baseBids;
    private transient MapState<Double, Long> baseAsks;
    private transient ListState<CombinationOrderEvent> activeCombOrders;
    private transient MapState<String, Double> contractBestPrices; // 存储最优价格用于广播

    private static final long SNAPSHOT_INTERVAL_MS = 500; // 0.5秒快照间隔

    @Override
    public void open(Configuration parameters) throws Exception {
        MapStateDescriptor<Double, Long> bidsDescriptor =
                new MapStateDescriptor<>("base-bids", Double.class, Long.class);
        baseBids = getRuntimeContext().getMapState(bidsDescriptor);

        MapStateDescriptor<Double, Long> asksDescriptor =
                new MapStateDescriptor<>("base-asks", Double.class, Long.class);
        baseAsks = getRuntimeContext().getMapState(asksDescriptor);

        ListStateDescriptor<CombinationOrderEvent> combOrdersDescriptor =
                new ListStateDescriptor<>("active-comb-orders", CombinationOrderEvent.class);
        activeCombOrders = getRuntimeContext().getListState(combOrdersDescriptor);

        MapStateDescriptor<String, Double> pricesDescriptor =
                new MapStateDescriptor<>("contract-best-prices", String.class, Double.class);
        contractBestPrices = getRuntimeContext().getMapState(pricesDescriptor);
    }

    @Override
    public void processElement(Object value, Context ctx, Collector<OrderBookSnapshot> out) throws Exception {
        if (value instanceof SingleLegOrderEvent) {
            processSingleLegOrder((SingleLegOrderEvent) value, ctx);
        } else if (value instanceof CombinationOrderEvent) {
            processCombinationOrder((CombinationOrderEvent) value, ctx);
        }

        // 注册定时器（如果还没有注册的话）
        long currentTime = ctx.timerService().currentProcessingTime();
        long nextSnapshotTime = ((currentTime / SNAPSHOT_INTERVAL_MS) + 1) * SNAPSHOT_INTERVAL_MS;
        ctx.timerService().registerProcessingTimeTimer(nextSnapshotTime);
    }

    private void processSingleLegOrder(SingleLegOrderEvent order, Context ctx) throws Exception {
        String contractCode = order.getContract_cde();
        double price = order.getOrd_prc();
        long remainingVol = order.getRmn_vol();
        String orderStatus = order.getOrd_sts();
        String buySellTag = order.getB_s_tag();

        // 根据订单状态和剩余数量更新订单簿
        if ("5".equals(orderStatus) || remainingVol == 0) {
            // 订单已撤销或完全成交，从订单簿中移除
            if ("B".equals(buySellTag)) {
                baseBids.remove(price);
            } else {
                baseAsks.remove(price);
            }
        } else {
            // 更新订单簿
            if ("B".equals(buySellTag)) {
                if (remainingVol > 0) {
                    baseBids.put(price, remainingVol);
                }
            } else {
                if (remainingVol > 0) {
                    baseAsks.put(price, remainingVol);
                }
            }
        }

        // 更新最优价格缓存
        updateBestPrices(contractCode);
    }

    private void processCombinationOrder(CombinationOrderEvent order, Context ctx) throws Exception {
        String orderStatus = order.getOrd_sts();

        if ("5".equals(orderStatus) || order.getRmn_vol() == 0) {
            // 移除已撤销或完成的组合订单
            List<CombinationOrderEvent> updatedOrders = new ArrayList<>();
            for (CombinationOrderEvent existingOrder : activeCombOrders.get()) {
                if (!existingOrder.getOrd_nbr().equals(order.getOrd_nbr())) {
                    updatedOrders.add(existingOrder);
                }
            }
            activeCombOrders.clear();
            activeCombOrders.addAll(updatedOrders);
        } else {
            // 更新或添加活跃的组合订单
            List<CombinationOrderEvent> updatedOrders = new ArrayList<>();
            boolean found = false;
            for (CombinationOrderEvent existingOrder : activeCombOrders.get()) {
                if (existingOrder.getOrd_nbr().equals(order.getOrd_nbr())) {
                    updatedOrders.add(order);
                    found = true;
                } else {
                    updatedOrders.add(existingOrder);
                }
            }
            if (!found) {
                updatedOrders.add(order);
            }
            activeCombOrders.clear();
            activeCombOrders.addAll(updatedOrders);
        }
    }

    private void updateBestPrices(String contractCode) throws Exception {
        Double bestBid = getBestPrice(baseBids, true);
        Double bestAsk = getBestPrice(baseAsks, false);

        if (bestBid != null) {
            contractBestPrices.put(contractCode + "_BID", bestBid);
        }
        if (bestAsk != null) {
            contractBestPrices.put(contractCode + "_ASK", bestAsk);
        }
    }

    private Double getBestPrice(MapState<Double, Long> priceMap, boolean isBid) throws Exception {
        List<Double> prices = new ArrayList<>();
        for (Map.Entry<Double, Long> entry : priceMap.entries()) {
            if (entry.getValue() > 0) {
                prices.add(entry.getKey());
            }
        }

        if (prices.isEmpty()) return null;

        if (isBid) {
            return Collections.max(prices); // 买单取最高价
        } else {
            return Collections.min(prices); // 卖单取最低价
        }
    }

    @Override
    public void onTimer(long timestamp, OnTimerContext ctx, Collector<OrderBookSnapshot> out) throws Exception {
        String contractCode = ctx.getCurrentKey();

        // 1. 创建基础层快照
        OrderBookSnapshot snapshot = new OrderBookSnapshot(contractCode);

        // 复制基础买单簿
        for (Map.Entry<Double, Long> entry : baseBids.entries()) {
            if (entry.getValue() > 0) {
                snapshot.getBids().put(entry.getKey(), entry.getValue());
            }
        }

        // 复制基础卖单簿
        for (Map.Entry<Double, Long> entry : baseAsks.entries()) {
            if (entry.getValue() > 0) {
                snapshot.getAsks().put(entry.getKey(), entry.getValue());
            }
        }

        // 2. 计算并叠加虚拟层
        for (CombinationOrderEvent combOrder : activeCombOrders.get()) {
            if (combOrder.getRmn_vol() > 0) {
                addVirtualOrdersToSnapshot(snapshot, combOrder);
            }
        }

        snapshot.setTimestamp(timestamp);
        out.collect(snapshot);

        // 注册下一个定时器
        ctx.timerService().registerProcessingTimeTimer(timestamp + SNAPSHOT_INTERVAL_MS);
    }

    private void addVirtualOrdersToSnapshot(OrderBookSnapshot snapshot, CombinationOrderEvent combOrder) throws Exception {
        String leg1 = combOrder.getLeg_1_contract_cde();
        String leg2 = combOrder.getLeg_2_contract_cde();
        String buySellTag = combOrder.getB_s_tag();
        double spreadPrice = combOrder.getOrd_prc();
        long volume = combOrder.getRmn_vol();

        // 获取另一条腿的最优价格
        Double leg1BestBid = contractBestPrices.get(leg1 + "_BID");
        Double leg1BestAsk = contractBestPrices.get(leg1 + "_ASK");
        Double leg2BestBid = contractBestPrices.get(leg2 + "_BID");
        Double leg2BestAsk = contractBestPrices.get(leg2 + "_ASK");

        // 根据组合策略计算虚拟订单价格
        // 这里以跨期套利(SPD)为例：买入价差 = 买Leg1，卖Leg2
        if ("B".equals(buySellTag)) { // 买入价差
            if (leg2BestAsk != null) {
                double virtualLeg1Price = leg2BestAsk - spreadPrice;
                addVirtualOrder(snapshot, virtualLeg1Price, volume, true);
            }
            if (leg1BestBid != null) {
                double virtualLeg2Price = leg1BestBid + spreadPrice;
                addVirtualOrder(snapshot, virtualLeg2Price, volume, false);
            }
        } else { // 卖出价差
            if (leg2BestBid != null) {
                double virtualLeg1Price = leg2BestBid - spreadPrice;
                addVirtualOrder(snapshot, virtualLeg1Price, volume, false);
            }
            if (leg1BestAsk != null) {
                double virtualLeg2Price = leg1BestAsk + spreadPrice;
                addVirtualOrder(snapshot, virtualLeg2Price, volume, true);
            }
        }
    }

    private void addVirtualOrder(OrderBookSnapshot snapshot, double price, long volume, boolean isBid) {
        if (isBid) {
            snapshot.getBids().merge(price, volume, Long::sum);
        } else {
            snapshot.getAsks().merge(price, volume, Long::sum);
        }
    }
}
