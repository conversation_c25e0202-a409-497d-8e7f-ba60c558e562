package com.futures.metric;

/**
 * 系统健康检查
 */
public class HealthChecker {
    private long lastSnapshotTime = 0;
    private long lastTradeTime = 0;
    private final long HEALTH_CHECK_INTERVAL = 30000; // 30秒

    public boolean isSystemHealthy() {
        long currentTime = System.currentTimeMillis();

        // 检查是否有数据流入
        if (currentTime - lastSnapshotTime > HEALTH_CHECK_INTERVAL) {
            // 可能存在数据延迟问题
            return false;
        }

        if (currentTime - lastTradeTime > HEALTH_CHECK_INTERVAL) {
            // 可能存在交易数据延迟问题
            return false;
        }

        return true;
    }

    public void updateSnapshotTime() {
        this.lastSnapshotTime = System.currentTimeMillis();
    }

    public void updateTradeTime() {
        this.lastTradeTime = System.currentTimeMillis();
    }
}
