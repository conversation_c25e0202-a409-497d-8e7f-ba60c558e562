package com.futures.metric;

// ============ 5. 监控和指标 ============

import org.apache.flink.metrics.Counter;
import org.apache.flink.metrics.MetricGroup;

/**
 * 自定义监控指标
 */
public class MonitoringMetrics {
    private Counter orderProcessedCounter;
    private Counter tradeProcessedCounter;
    private Counter snapshotGeneratedCounter;
    private Counter pnlCalculatedCounter;

    public void registerMetrics(MetricGroup metricGroup) {
        this.orderProcessedCounter = metricGroup.counter("orders_processed");
        this.tradeProcessedCounter = metricGroup.counter("trades_processed");
        this.snapshotGeneratedCounter = metricGroup.counter("snapshots_generated");
        this.pnlCalculatedCounter = metricGroup.counter("pnl_calculated");
    }

    public void incrementOrderProcessed() {
        if (orderProcessedCounter != null) {
            orderProcessedCounter.inc();
        }
    }

    public void incrementTradeProcessed() {
        if (tradeProcessedCounter != null) {
            tradeProcessedCounter.inc();
        }
    }

    public void incrementSnapshotGenerated() {
        if (snapshotGeneratedCounter != null) {
            snapshotGeneratedCounter.inc();
        }
    }

    public void incrementPnLCalculated() {
        if (pnlCalculatedCounter != null) {
            pnlCalculatedCounter.inc();
        }
    }
}
