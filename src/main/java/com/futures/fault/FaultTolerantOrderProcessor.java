package com.futures.fault;

// ============ 6. 错误处理和容错机制 ============

import com.futures.datagen.model.CombinationOrderEvent;
import com.futures.datagen.model.SingleLegOrderEvent;
import com.futures.job.FuturesDataReconstructionJob;
import org.apache.flink.streaming.api.functions.ProcessFunction;
import org.apache.flink.util.Collector;
import org.apache.flink.util.OutputTag;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;


/**
 * 带容错的数据处理函数
 */
public class FaultTolerantOrderProcessor extends ProcessFunction<String, Object> {
    private static final Logger logger = LoggerFactory.getLogger(FaultTolerantOrderProcessor.class);
    private static final OutputTag<String> errorOutputTag = new OutputTag<String>("errors"){};

    @Override
    public void processElement(String jsonStr, Context ctx, Collector<Object> out) throws Exception {
        try {
            // 尝试解析数据
            if (jsonStr.contains("leg_1_contract_cde")) {
                // 组合订单
                CombinationOrderEvent event = FuturesDataReconstructionJob.parseCombinationOrder(jsonStr);
                out.collect(event);
            } else if (jsonStr.contains("contract_cde")) {
                // 单腿订单
                SingleLegOrderEvent event = FuturesDataReconstructionJob.parseSingleLegOrder(jsonStr);
                out.collect(event);
            } else {
                logger.warn("Unknown order type: {}", jsonStr);
                ctx.output(errorOutputTag, "Unknown order type: " + jsonStr);
            }
        } catch (Exception e) {
            logger.error("Error processing order: {}", jsonStr, e);
            ctx.output(errorOutputTag, "Error: " + e.getMessage() + ", Data: " + jsonStr);
            // 不重新抛出异常，继续处理下一条数据
        }
    }

    public OutputTag<String> getErrorOutputTag() {
        return errorOutputTag;
    }
}
